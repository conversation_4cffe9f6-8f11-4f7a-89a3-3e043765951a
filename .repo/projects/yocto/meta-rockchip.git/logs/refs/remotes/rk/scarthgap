0000000000000000000000000000000000000000 5466ca1d8d570c840ad2d00746071f72961daa7c Montex.CC <<EMAIL>> 1744609230 +0800	fetch --depth=2147483647 --quiet --progress rk --tags +refs/heads/scarthgap:refs/remotes/rk/scarthgap +refs/tags/*:refs/tags/*: storing head
5466ca1d8d570c840ad2d00746071f72961daa7c 97f8ada9a91cec7520b32fa92bb2cbfa8232b610 Montex.CC <<EMAIL>> 1745979160 +0800	fetch --depth=1 --quiet --progress rk --prune --no-tags 97f8ada9a91cec7520b32fa92bb2cbfa8232b610 scarthgap: forced-update
97f8ada9a91cec7520b32fa92bb2cbfa8232b610 e201b048e50037d7c219521338d31817dffed9be Montex.CC <<EMAIL>> 1747648307 +0800	fetch --depth=2147483647 --quiet --progress rk --tags +refs/heads/scarthgap:refs/remotes/rk/scarthgap +refs/tags/*:refs/tags/*: fast-forward
e201b048e50037d7c219521338d31817dffed9be bf91141158dcc7eb3de2a907bd07f4dea35ff351 Montex.CC <<EMAIL>> 1750146096 +0800	fetch --depth=1 --quiet --progress rk --prune --no-tags bf91141158dcc7eb3de2a907bd07f4dea35ff351 scarthgap: forced-update
bf91141158dcc7eb3de2a907bd07f4dea35ff351 8aa6316db2591deed11b594d6aaebc65cbd6c30f Montex.CC <<EMAIL>> 1753340865 +0800	fetch --quiet --progress rk --prune --tags +refs/heads/*:refs/remotes/rk/* +refs/tags/meta-rockchip-linux-6.1-stan-rkr6:refs/tags/meta-rockchip-linux-6.1-stan-rkr6 +refs/tags/*:refs/tags/*: fast-forward
