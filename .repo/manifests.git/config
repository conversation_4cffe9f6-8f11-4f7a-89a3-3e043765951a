[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "default"]
	remote = origin
	merge = refs/heads/rv1126b
[repo "syncstate.main"]
	synctime = 2025-08-25T08:20:40.562881Z
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo/repo/main.py', '--repo-dir=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo', '--wrapper-version=2.17', '--wrapper-path=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo/repo/repo', '--', 'sync', '-c']
[repo "syncstate.superproject"]
	superproject = false
	haslocalmanifests = true
	hassuperprojecttag = false
[repo "syncstate.options"]
	jobs = 4
	localonly = true
	mpupdate = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
	forcesync = true
	currentbranchonly = true
[repo "syncstate.remote.origin"]
	url = https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/rv1126b
