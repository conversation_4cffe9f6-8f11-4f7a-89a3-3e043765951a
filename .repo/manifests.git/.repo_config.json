{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "branch.default.remote": ["origin"], "branch.default.merge": ["refs/heads/rv1126b"], "repo.syncstate.main.synctime": ["2025-08-25T08:19:47.410225Z"], "repo.syncstate.main.version": ["1"], "repo.syncstate.sys.argv": ["['/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo/repo/main.py', '--repo-dir=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo', '--wrapper-version=2.17', '--wrapper-path=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release_v1.0.0_20250620_sync20250728/.repo/repo/repo', '--', 'sync', '--force-sync', 'app/rkipc']"], "repo.syncstate.superproject.superproject": ["false"], "repo.syncstate.superproject.haslocalmanifests": ["true"], "repo.syncstate.superproject.hassuperprojecttag": ["false"], "repo.syncstate.options.jobs": ["4"], "repo.syncstate.options.localonly": ["true"], "repo.syncstate.options.mpupdate": ["true"], "repo.syncstate.options.clonebundle": ["true"], "repo.syncstate.options.retryfetches": ["0"], "repo.syncstate.options.prune": ["true"], "repo.syncstate.options.repoverify": ["true"], "repo.syncstate.options.quiet": ["false"], "repo.syncstate.options.verbose": ["false"], "repo.syncstate.options.forcesync": ["true"], "repo.syncstate.options.currentbranchonly": ["true"], "repo.syncstate.remote.origin.url": ["https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests"], "repo.syncstate.remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "repo.syncstate.branch.default.remote": ["origin"], "repo.syncstate.branch.default.merge": ["refs/heads/rv1126b"]}