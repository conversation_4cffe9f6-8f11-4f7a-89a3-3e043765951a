<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="rk" fetch="../../" />
  <default remote="rk" sync-j="4"/>

  <!-- Based file name： RV1126B_LINUX6.1_SDK_BETA_V0.1.0_20250515 -->
  <include name="include/rv1126b_doc-b1.xml"/>
  <include name="common/linux6.1/linux6.1-rkb1.xml"/>
  <include name="common/yocto/yocto-scarthgap-beta-b1.xml"/>
  <include name="common/debian/debian12-rkb1.xml"/>
  <include name="common/ipc/rkipc-b1.xml"/>

  <!-- device/rockchip for rv1126b -->
  <project name="linux/device/rockchip" path="device/rockchip" revision="edefc71ee7fdce820c676b8a95e822ca642006cd" upstream="rv1126b" dest-branch="rv1126b" clone-depth="1">
    <linkfile src="common/scripts/build.sh" dest="build.sh"/>
    <linkfile src="common/Makefile" dest="Makefile"/>
    <linkfile src="common/scripts/rkflash.sh" dest="rkflash.sh"/>
  </project>

  <project name="linux/external/camera_engine_rkaiq" path="external/camera_engine_rkaiq" revision="af7d3650e2ed56b37c7187cd30685b21265a3693" upstream="rv1106" dest-branch="rv1106" clone-depth="1"/>

</manifest>
