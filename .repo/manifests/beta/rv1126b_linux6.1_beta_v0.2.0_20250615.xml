<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="rk" fetch="../../" />
  <default remote="rk" sync-j="4"/>

  <!-- Based file name： RV1126B_LINUX6.1_SDK_BETA_V0.2.0_20250615 -->
  <include name="include/rv1126b_doc-b2.xml"/>
  <include name="common/linux6.1/linux6.1-rkb2.xml"/>
  <include name="common/yocto/yocto-scarthgap-beta-b2.xml"/>
  <include name="common/debian/debian12-rkb2.xml"/>
  <include name="common/ipc/rkipc-b2.xml"/>
  <include name="common/amp/amp-riscv-4.1-b2.xml"/>

  <!-- device/rockchip for rv1126b -->
  <project name="linux/device/rockchip" path="device/rockchip" revision="4540b75a7fb44c055ce70463d93eabe5bf0c61ee" upstream="rv1126b" dest-branch="rv1126b" clone-depth="1">
    <linkfile src="common/scripts/build.sh" dest="build.sh"/>
    <linkfile src="common/Makefile" dest="Makefile"/>
    <linkfile src="common/scripts/rkflash.sh" dest="rkflash.sh"/>
  </project>

  <project name="linux/external/camera_engine_rkaiq" path="external/camera_engine_rkaiq" revision="d9908a1c918b227b0b54ff4da6958b75f19a3ddd" upstream="rv1106" dest-branch="rv1106" clone-depth="1"/>

</manifest>
