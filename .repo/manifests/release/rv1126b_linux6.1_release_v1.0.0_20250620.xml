<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="rk" fetch="../../" />
  <default remote="rk" sync-j="4"/>

  <!-- Based file name： RV1126B_LINUX6.1_SDK_RELEASE_V1.0.0_20250620 -->
  <include name="include/rv1126b_doc-r1.xml"/>
  <include name="common/linux6.1/linux6.1-rkr1.xml"/>
  <include name="common/yocto/yocto-scarthgap-release-r1.xml"/>
  <include name="common/debian/debian12-rkr1.xml"/>
  <include name="common/ipc/rkipc-r1.xml"/>
  <include name="common/amp/amp-riscv-4.1-r1.xml"/>

  <!-- device/rockchip for rv1126b -->
  <project name="linux/device/rockchip" path="device/rockchip" revision="refs/tags/rv1126b-linux-6.1-stan-rkr6" clone-depth="1">
    <linkfile src="common/scripts/build.sh" dest="build.sh"/>
    <linkfile src="common/Makefile" dest="Makefile"/>
    <linkfile src="common/scripts/rkflash.sh" dest="rkflash.sh"/>
  </project>

  <project name="linux/external/camera_engine_rkaiq" path="external/camera_engine_rkaiq" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/rknn-llm" path="external/rknn-llm" revision="refs/tags/linux-6.1-stan-rkr6.1" clone-depth="1"/>
  <project name="rk/rknn-toolkit2" path="external/rknn-toolkit2" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/rknpu2" path="external/rknpu2" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>

</manifest>
