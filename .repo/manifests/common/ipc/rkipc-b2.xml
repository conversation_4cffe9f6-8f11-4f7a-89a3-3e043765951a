<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/app/rkipc" path="app/rkipc" revision="b818f9b0003770cb94b97ccf28fc0bea25a803ab" upstream="master" dest-branch="master"/>
  <project name="linux/app/web-new/mini_ipcweb-backend" path="app/ipcweb-backend" revision="a7f99cab93fc1d24da25bf02aa201abfd46c7647" upstream="master" dest-branch="master"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="ba1c01ac5a5eb75644b6320bf05cc637494d773f" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="a3d096ddd18e19103758bf15807a4bfe45c3c462" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/samples-ng" path="external/samples" revision="c0f244b45280675d09333058cef868fd0cf48dc4" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="62083356c5d1d79bfa04014aa980e4b150ad5b13" upstream="rv1126b-buildroot" dest-branch="rv1126b-buildroot" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="5f0fa4698d5305d8653c2352bfcf1d9aa57064a4" upstream="master" dest-branch="master"/>
  <project name="linux/bsp/rkadk" path="app/rkadk" revision="bf0861be7220315c4aeff1feb757674b5b096784" upstream="develop" dest-branch="develop"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="35b8424ca73b07177766e16e0fd5f2b74be4e3bb" upstream="master" dest-branch="master"/>
  <project name="linux/rockit" path="external/rockit" revision="4684c09d5235f63b1d52f182d0529aebe09e37e7" upstream="rockit-c" dest-branch="rockit-c"/>
</manifest>
