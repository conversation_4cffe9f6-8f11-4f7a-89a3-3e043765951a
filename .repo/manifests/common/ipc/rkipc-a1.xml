<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/app/rkipc" path="app/rkipc" revision="125d203dee0643bf12cd9c569b48b5b0dcad78bf" upstream="master" dest-branch="master"/>
  <project name="linux/app/web-new/ipcweb-backend" path="app/ipcweb-backend" revision="42d23ee928d8e3c93c1c98968b2ade8657d0ec61" upstream="master" dest-branch="master"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="ba1c01ac5a5eb75644b6320bf05cc637494d773f" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="a3de76ce4e8c12b3f734f2f250a8359a7f9fbfc1" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/samples-ng" path="external/samples" revision="7837d4ae9666dc98bb2d4330019ec0ae4175c4c7" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="e3e2462bc20ec405846490078afb3778fdbcc775" upstream="rv1126b-buildroot" dest-branch="rv1126b-buildroot" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="5f0fa4698d5305d8653c2352bfcf1d9aa57064a4" upstream="master" dest-branch="master"/>
  <project name="linux/bsp/rkadk" path="app/rkadk" revision="2fabdb50526be786c2a7547437cb1100fc154550" upstream="develop" dest-branch="develop"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="31ef4a6a53b461d8e145020d89480c42f375f2b3" upstream="master" dest-branch="master"/>
  <project name="linux/rockit" path="external/rockit" revision="9247c8d1e1ac7252934d7f7a5f904c8f0ff00b36" upstream="rockit-c" dest-branch="rockit-c"/>
</manifest>
