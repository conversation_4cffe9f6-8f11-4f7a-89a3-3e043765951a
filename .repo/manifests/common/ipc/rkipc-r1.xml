<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/ipc/app/rkipc" path="app/rkipc" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/app/web-new/mini_ipcweb-backend" path="app/ipcweb-backend" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="a3d096ddd18e19103758bf15807a4bfe45c3c462" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/samples-ng" path="external/samples" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="refs/tags/linux-6.1-stan-rkr6.1" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/bsp/rkadk" path="app/rkadk" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/rockit" path="external/rockit" revision="9ea9cc2f1c55905f6d63226de521eb5f673dc15e" upstream="rockit-c" dest-branch="rockit-c"/>
</manifest>
