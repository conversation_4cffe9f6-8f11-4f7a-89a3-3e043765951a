<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/app/rkipc" path="app/rkipc" revision="2926501feee6ae045e7637ca7b5cafd599a8cb11" upstream="master" dest-branch="master"/>
  <project name="linux/app/web-new/mini_ipcweb-backend" path="app/ipcweb-backend" revision="a7f99cab93fc1d24da25bf02aa201abfd46c7647" upstream="master" dest-branch="master"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="ba1c01ac5a5eb75644b6320bf05cc637494d773f" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="0382c430ee159b26d88c567472a0b5a88f7af1d0" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/samples-ng" path="external/samples" revision="774ee6b071d46d1ce2e469fb902454e52c951a53" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="a0609538ae2a961841511507091b932d1e31d273" upstream="rv1126b-buildroot" dest-branch="rv1126b-buildroot" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="5f0fa4698d5305d8653c2352bfcf1d9aa57064a4" upstream="master" dest-branch="master"/>
  <project name="linux/bsp/rkadk" path="app/rkadk" revision="bd897c03e743dd37bf31cf591ca5e19ad76028d3" upstream="develop" dest-branch="develop"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="35b8424ca73b07177766e16e0fd5f2b74be4e3bb" upstream="master" dest-branch="master"/>
  <project name="linux/rockit" path="external/rockit" revision="e435e2ead49a3d74e9083a42f95bb5f0974550d7" upstream="rockit-c" dest-branch="rockit-c"/>
</manifest>
