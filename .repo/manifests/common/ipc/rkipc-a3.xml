<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/app/rkipc" path="app/rkipc" revision="36ea0e6546c419731e3552ea16a7dff934ccf9a4" upstream="master" dest-branch="master"/>
  <project name="linux/app/web-new/mini_ipcweb-backend" path="app/ipcweb-backend" revision="08a4e5bff336996902175bbc684592ae00478930" upstream="master" dest-branch="master"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="ba1c01ac5a5eb75644b6320bf05cc637494d773f" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="4a6a6a3463258bf132914cfe7722c3c5b22b6fbb" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/samples-ng" path="external/samples" revision="2814415d667c1fb187222273e0c0fdc38aaa4f30" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="be47efbc0dd8206cba5fd94ce349cb72e79bd595" upstream="rv1126b-buildroot" dest-branch="rv1126b-buildroot" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="5f0fa4698d5305d8653c2352bfcf1d9aa57064a4" upstream="master" dest-branch="master"/>
  <project name="linux/bsp/rkadk" path="app/rkadk" revision="a204e7ad9c4d86b0575176f7c984cca40102e089" upstream="develop" dest-branch="develop"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="31ef4a6a53b461d8e145020d89480c42f375f2b3" upstream="master" dest-branch="master"/>
  <project name="linux/rockit" path="external/rockit" revision="e9d641b3a19d94aa2a9953070bd62fc0add745ea" upstream="rockit-c" dest-branch="rockit-c"/>
</manifest>
