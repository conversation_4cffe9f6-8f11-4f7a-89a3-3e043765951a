<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="rk/mcu/hal" path="hal" revision="4fa5054abf00afd1cb0d611a0d537f1e2aeb42ba" upstream="master" dest-branch="master" clone-depth="1">
    <linkfile src="." dest="rtos/bsp/rockchip/common/hal"/>
  </project>
  <project name="rk/mcu/hal/applications" path="hal/applications" revision="c5927d3070ebc810b2920f9162bbdada74e63acb" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/mcu/hal/middleware" path="hal/middleware" revision="6194c06052347d92f3c57064a2e5e9b919b2d5f6" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/prebuilts/xpack-riscv-none-embed-gcc-10.2.0-1.2-linux-x64" path="prebuilts/gcc/linux-x86/riscv/xpack-riscv-none-embed-gcc-10.2.0-1.2" revision="8a83b315a3c06c28431683f4a7c1ef9ff5de6670" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rtos/rt-thread/docs" path="docs/rtt-docs" revision="bb36bf7c50cbe75b57b15affaa279855535f17b3" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rtos/rt-thread/rt-thread" path="rtos" revision="88c7e5a6a99ba0fd3cedfbf4c00be13fca2529d4" upstream="develop-v4.1.x" dest-branch="develop-v4.1.x" clone-depth="1"/>
  <project name="rtos/rt-thread/rtthread-apps" path="rtos/applications/rtthread-apps" revision="525c38360aa48b77535294d6e58d6b0cdf776539" upstream="master" dest-branch="master" clone-depth="1"/>
</manifest>
