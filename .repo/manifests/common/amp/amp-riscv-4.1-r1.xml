<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="rk/mcu/hal" path="hal" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1">
    <linkfile src="." dest="rtos/bsp/rockchip/common/hal"/>
  </project>
  <project name="rk/mcu/hal/applications" path="hal/applications" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/mcu/hal/middleware" path="hal/middleware" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/prebuilts/xpack-riscv-none-embed-gcc-10.2.0-1.2-linux-x64" path="prebuilts/gcc/linux-x86/riscv/xpack-riscv-none-embed-gcc-10.2.0-1.2" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rtos/rt-thread/docs" path="docs/rtt-docs" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rtos/rt-thread/rt-thread" path="rtos" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rtos/rt-thread/rtthread-apps" path="rtos/applications/rtthread-apps" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
</manifest>
