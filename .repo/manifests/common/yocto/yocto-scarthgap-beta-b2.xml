<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/build" path="yocto/build/conf" revision="660aecbf602e9810c6091fd25404b4d05f4dc097" upstream="scarthgap" dest-branch="scarthgap" clone-depth="1"/>
  <project name="linux/poky" path="yocto/meta-browser" revision="bb8662912354dae13634c0ec35c3803c344b1e72" upstream="scarthgap/meta-browser" dest-branch="scarthgap/meta-browser" clone-depth="1"/>
  <project name="linux/poky" path="yocto/meta-clang" revision="b9ef02282197380ef05edbd0eb852e1934ceb59b" upstream="scarthgap/meta-clang" dest-branch="scarthgap/meta-clang" clone-depth="1"/>
  <project name="linux/poky" path="yocto/meta-openembedded" revision="edd1a1e284fdcb80cd48d411f235d47f23bc27ae" upstream="scarthgap/meta-openembedded" dest-branch="scarthgap/meta-openembedded" clone-depth="1"/>
  <project name="linux/poky" path="yocto/meta-lts-mixins" revision="1793a1b8fc92cf8688c72b7fd4181e3a2f5ade55" upstream="scarthgap/meta-lts-mixins" dest-branch="scarthgap/meta-lts-mixins" clone-depth="1"/>
  <project name="linux/poky" path="yocto/poky" revision="9036bc090c771c710796e3690da59d3ebb96dcf1" upstream="scarthgap/poky" dest-branch="scarthgap/poky" clone-depth="1">
    <linkfile src="scripts" dest="yocto/scripts"/>
    <linkfile src="bitbake" dest="yocto/bitbake"/>
    <linkfile src="meta-poky" dest="yocto/meta-poky"/>
    <linkfile src=".templateconf" dest="yocto/.templateconf"/>
    <linkfile src="oe-init-build-env" dest="yocto/oe-init-build-env"/>
  </project>
  <project name="linux/yocto/meta-rockchip" path="yocto/meta-rockchip" revision="bf91141158dcc7eb3de2a907bd07f4dea35ff351" upstream="scarthgap" dest-branch="scarthgap" clone-depth="1"/>
</manifest>
