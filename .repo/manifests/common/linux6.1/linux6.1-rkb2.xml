<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="android/rk/platform/system/rk_tee_user" path="external/security/rk_tee_user" revision="e92df7e3b1a5b97a15216943d617098fe3bdf8df" upstream="develop-next" dest-branch="develop-next" clone-depth="1"/>
  <project name="android/rk/u-boot" path="u-boot" revision="feb2c9de35d4e803e1d4ed5314a37440ec175e24" upstream="next-dev" dest-branch="next-dev"/>
  <project name="linux/app/lvgl_demo" path="app/lvgl_demo" revision="24d6e6db466f04ce0857d703b349db1905ff83cc" upstream="develop" dest-branch="develop"/>
  <project name="linux/buildroot" path="buildroot" revision="d2edf395a977d4a053f973d109420a49d69fd3bf" upstream="rockchip/2024.02" dest-branch="rockchip/2024.02"/>
  <project name="linux/external/alsa-config" path="external/alsa-config" revision="5529257039c41955724c1069796f5cb7b2015c77" upstream="master" dest-branch="master"/>
  <project name="linux/external/common_algorithm" path="external/common_algorithm" revision="9806dffaf0db84f36ac7832d429f8aa01ac2526d" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rktoolkit" path="external/rktoolkit" revision="bc1d85d6d2a39c67ed2d619f6faf1a996d542722" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkupdate" path="external/rkupdate" revision="e9cd4fe07733f807057fd7a6bd5e8d42a59624a7" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkwifibt" path="external/rkwifibt" revision="3c3b9a88ff0ed4f9fe7ffe7cd13acd74cc9a97ef" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkwifibt-app" path="external/rkwifibt-app" revision="4168fb2dba5c62268d9a2d7e5dc59e3e1ec09f55" upstream="master" dest-branch="master"/>
  <project name="linux/gstreamer-rockchip" path="external/gstreamer-rockchip" revision="66be2501caa40e4abe93c889a7364af2497ea1d7" upstream="master" dest-branch="master"/>
  <project name="linux/linux-rga" path="external/linux-rga" revision="f3be6d8132b09a3b5569d1981a37fe627a27e370" upstream="multi_rga_im2d_api" dest-branch="multi_rga_im2d_api"/>
  <project name="linux/recovery" path="external/recovery" revision="4e70100a4d16306d6fadd5113c81effea8c166ae" upstream="develop" dest-branch="develop"/>
  <project name="linux/rockchip-test" path="external/rockchip-test" revision="67620815b76a24ac457e76f55b734a6d73d58701" upstream="master" dest-branch="master"/>
  <project name="linux/security/bin" path="external/security/bin" revision="24491471815d1dc92807df98b729dc0078004930" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/tools" path="tools" revision="90c8aa643d33c6fb931b4304d8f51d8725d3fdc4" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/kernel-stable" path="kernel-6.1" revision="bb36a2c6523c09aa8d5502be380bfa6a86651e9c" upstream="release-6.1" dest-branch="release-6.1">
    <linkfile src="." dest="kernel"/>
  </project>
  <project name="rk/librkcrypto" path="external/security/librkcrypto" revision="eddf58139ad8c4a4f1296777ff30a3445fd3f7c6" upstream="master" dest-branch="master"/>
  <project name="rk/mpp" path="external/mpp" revision="ed3995dc3287a76e0775a38dde7d5546c5d86f4d" upstream="develop" dest-branch="develop"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" revision="adbb295a970c4b39dc487c95226fe84d2c460072" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" path="prebuilts/gcc/linux-x86/arm/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" revision="2e89382ca3f8c34086272509ae4d7a5c340ffafc" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rkbin" path="rkbin" revision="74213af1e952c4683d2e35952507133b61394862" upstream="master" dest-branch="master" clone-depth="1"/>
</manifest>
