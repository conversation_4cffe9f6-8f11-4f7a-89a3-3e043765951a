<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="android/rk/platform/system/rk_tee_user" path="external/security/rk_tee_user" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="android/rk/u-boot" path="u-boot" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/app/lvgl_demo" path="app/lvgl_demo" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/buildroot" path="buildroot" revision="refs/tags/linux-6.1-stan-rkr6.1"/>
  <project name="linux/external/alsa-config" path="external/alsa-config" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/external/common_algorithm" path="external/common_algorithm" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="linux/external/rktoolkit" path="external/rktoolkit" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/external/rkupdate" path="external/rkupdate" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/external/rkwifibt" path="external/rkwifibt" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="linux/external/rkwifibt-app" path="external/rkwifibt-app" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/gstreamer-rockchip" path="external/gstreamer-rockchip" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/linux-rga" path="external/linux-rga" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/recovery" path="external/recovery" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/rockchip-test" path="external/rockchip-test" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="linux/security/bin" path="external/security/bin" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="linux/tools" path="tools" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/kernel-stable" path="kernel-6.1" revision="refs/tags/linux-6.1-stan-rkr6.1">
    <linkfile src="." dest="kernel"/>
  </project>
  <project name="rk/librkcrypto" path="external/security/librkcrypto" revision="refs/tags/linux-6.1-stan-rkr6"/>
  <project name="rk/mpp" path="external/mpp" revision="refs/tags/linux-6.1-stan-rkr6.1"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" path="prebuilts/gcc/linux-x86/arm/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
  <project name="rk/rkbin" path="rkbin" revision="refs/tags/linux-6.1-stan-rkr6" clone-depth="1"/>
</manifest>
