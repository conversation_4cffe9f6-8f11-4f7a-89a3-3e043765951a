<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="android/rk/platform/system/rk_tee_user" path="external/security/rk_tee_user" revision="c8d8f22cb84a4fb419239e96ce549751375cd31c" upstream="develop-next" dest-branch="develop-next" clone-depth="1"/>
  <project name="android/rk/u-boot" path="u-boot" revision="41feb71f046418003a43b45ed96f40f89861c9ab" upstream="next-dev" dest-branch="next-dev"/>
  <project name="linux/app/lvgl_demo" path="app/lvgl_demo" revision="24d6e6db466f04ce0857d703b349db1905ff83cc" upstream="develop" dest-branch="develop"/>
  <project name="linux/buildroot" path="buildroot" revision="42cdf609aac3047ea453118dde81d8ecfe324542" upstream="rockchip/2024.02" dest-branch="rockchip/2024.02"/>
  <project name="linux/external/alsa-config" path="external/alsa-config" revision="5529257039c41955724c1069796f5cb7b2015c77" upstream="master" dest-branch="master"/>
  <project name="linux/external/common_algorithm" path="external/common_algorithm" revision="9806dffaf0db84f36ac7832d429f8aa01ac2526d" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rktoolkit" path="external/rktoolkit" revision="bc1d85d6d2a39c67ed2d619f6faf1a996d542722" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkupdate" path="external/rkupdate" revision="e9cd4fe07733f807057fd7a6bd5e8d42a59624a7" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkwifibt" path="external/rkwifibt" revision="95c0b195429080c7e793ebef0fd27309ca9bf4b5" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkwifibt-app" path="external/rkwifibt-app" revision="5ca9c78c9c065d4bda11b9cca3cc5cf9d51e8b78" upstream="master" dest-branch="master"/>
  <project name="linux/gstreamer-rockchip" path="external/gstreamer-rockchip" revision="7ffd7f40576bbfb861bc7a7d3492c710d149aff8" upstream="master" dest-branch="master"/>
  <project name="linux/linux-rga" path="external/linux-rga" revision="f3be6d8132b09a3b5569d1981a37fe627a27e370" upstream="multi_rga_im2d_api" dest-branch="multi_rga_im2d_api"/>
  <project name="linux/recovery" path="external/recovery" revision="4e70100a4d16306d6fadd5113c81effea8c166ae" upstream="develop" dest-branch="develop"/>
  <project name="linux/rockchip-test" path="external/rockchip-test" revision="18352108eeb0664ff8337af8da53e88d366bd532" upstream="master" dest-branch="master"/>
  <project name="linux/security/bin" path="external/security/bin" revision="24491471815d1dc92807df98b729dc0078004930" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/tools" path="tools" revision="90c8aa643d33c6fb931b4304d8f51d8725d3fdc4" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/kernel-stable" path="kernel-6.1" revision="38afd05b0da50ac2da03c10bc4ba914f8e6a3951" upstream="release-6.1" dest-branch="release-6.1">
    <linkfile src="." dest="kernel"/>
  </project>
  <project name="rk/librkcrypto" path="external/security/librkcrypto" revision="eddf58139ad8c4a4f1296777ff30a3445fd3f7c6" upstream="master" dest-branch="master"/>
  <project name="rk/mpp" path="external/mpp" revision="161345e67f31afddeb0509e8ecfc257c603fb123" upstream="develop" dest-branch="develop"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" revision="adbb295a970c4b39dc487c95226fe84d2c460072" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" path="prebuilts/gcc/linux-x86/arm/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" revision="2e89382ca3f8c34086272509ae4d7a5c340ffafc" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rkbin" path="rkbin" revision="924f78fe9af44111bb97feb2bc7d8d795769686a" upstream="master" dest-branch="master" clone-depth="1"/>
</manifest>
