
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    access_log off;

    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Range,Content-Type,Authorization';
    add_header 'Access-Control-Expose-Headers' 'X-Location';

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip  on;
    gzip_min_length 20;
    gzip_comp_level 5;
    gzip_vary on;
    gzip_types application/javascript application/x-javascript text/javascript image/png image/x-icon;
    gzip_static on;
    gzip_buffers 2 4k;
    gzip_http_version 1.1;

    server {
        listen       80;
        server_name  localhost;

	location /live {
		flv_live on;
	}
        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root   /usr/www;
            index  index.html index.htm;
        }

	location ~* \.(mp4|bmp)$  {
	    root /usr/www;
            if ($request_uri ~* view$) {
		add_header Content-Disposition inline;
	    }
	    if ($request_uri !~* view$) {
		add_header Content-Disposition attachment;
	    }
	}

        # error_page  404              /404.html;
        error_page  404              /;

        # redirect server error pages to the static page /50x.html
        #
        # error_page   500 502 503 504  /50x.html;
        # location = /50x.html {
        #     root   html;
        # }

        location /cgi-bin/ {
            gzip           off;
            root           /usr/www;
            fastcgi_pass   unix:/run/fcgiwrap.sock;
            fastcgi_index  entry.cgi;
            fastcgi_param  DOCUMENT_ROOT     /usr/www/cgi-bin;
            fastcgi_param  SCRIPT_NAME       /entry.cgi;
            include        fastcgi_params;
            set $path_info "";
            set $real_script_name $fastcgi_script_name;
            if ($fastcgi_script_name ~ "^(.+?\.cgi)(/.+)$") {
                set $real_script_name $1;
                set $path_info $2;
            }
            fastcgi_param PATH_INFO       $path_info;
            fastcgi_param SCRIPT_FILENAME $document_root$real_script_name;
            fastcgi_param SCRIPT_NAME     $real_script_name;
        }

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }

    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

    #location /hls {
    #    types {
    #        application/vnd.apple.mpegurl m3u8;
    #    }
    #    root /tmp;
    #    add_header Cache-Control no-cache;

    #    # To avoid issues with cross-domain HTTP requests (e.g. during development)
    #    add_header Access-Control-Allow-Origin *;
    #}

}

rtmp {
    access_log off;
    server {
        listen 1935;
        chunk_size 4096;
        max_message 10485760;
        #allow publish 127.0.0.1;
        #deny publish all;

        application live {
            live on;
        }

        #application hls {
        #    live on;
        #    hls on;
        #    hls_path /tmp/hls;
        #    hls_fragment 1s;
        #}
    }
}
