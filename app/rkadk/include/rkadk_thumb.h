/*
 * Copyright (c) 2021 Rockchip, Inc. All Rights Reserved.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

#ifndef __RKADK_THUMB_H__
#define __RKADK_THUMB_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "rkadk_common.h"

// Default jpeg thumbnail
RKADK_S32 RKADK_GetThmInMp4(RKADK_U32 u32CamId, RKADK_CHAR *pszFileName,
                            RKADK_U8 *pu8Buf, RKADK_U32 *pu32Size);

RKADK_S32 RKADK_GetThmInMp4Ex(RKADK_U32 u32CamId, RKADK_CHAR *pszFileName,
                              RKADK_THUMB_ATTR_S *pstThumbAttr);

RKADK_S32 RKADK_ThmBufFree(RKADK_THUMB_ATTR_S *pstThumbAttr);

/* If the resolutions of the thumbnails are inconsistent,
 * the resolution at the time of creation must be the maximum resolution.
*/
RKADK_S32 RKADK_GetThmChnCreate(RKADK_U32 u32CamId, RKADK_THUMB_CHN_ATTR_S *pstThmChnAttr);

/* The parameters must be the same as when they were created */
RKADK_S32 RKADK_GetThmChnDestory(RKADK_U32 u32CamId, RKADK_THUMB_CHN_ATTR_S *pstThmChnAttr);

#ifdef __cplusplus
}
#endif
#endif
